package com.stpl.tech.kettle.converter;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import com.stpl.tech.kettle.cache.DispenserCanisterCache;
import com.stpl.tech.kettle.core.ReceiptType;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.core.notification.OrderInfoDomain;
import com.stpl.tech.kettle.data.kettle.CustomerInfo;
import com.stpl.tech.kettle.data.kettle.OrderItemDispenserShotsData;
import com.stpl.tech.kettle.data.kettle.OrderItemStatus;
import com.stpl.tech.kettle.data.kettle.TableOrderMappingDetail;
import com.stpl.tech.kettle.data.kettle.UnitTableMappingDetail;
import com.stpl.tech.kettle.delivery.model.DeliveryResponse;
import com.stpl.tech.kettle.domain.model.ActionDetail;
import com.stpl.tech.kettle.domain.model.CashCardType;
import com.stpl.tech.kettle.domain.model.ComboQunantityStrategy;
import com.stpl.tech.kettle.domain.model.ComplimentaryDetail;
import com.stpl.tech.kettle.domain.model.DiscountDetail;
import com.stpl.tech.kettle.domain.model.ExternalSettlement;
import com.stpl.tech.kettle.domain.model.MilkVariant;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderDomain;
import com.stpl.tech.kettle.domain.model.OrderFetchStrategy;
import com.stpl.tech.kettle.domain.model.OrderInvoice;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.OrderItemComposition;
import com.stpl.tech.kettle.domain.model.OrderItemDispenserShotsDataDto;
import com.stpl.tech.kettle.domain.model.OrderNotification;
import com.stpl.tech.kettle.domain.model.OrderPaymentDenomination;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.domain.model.PercentageDetail;
import com.stpl.tech.kettle.domain.model.ProductSource;
import com.stpl.tech.kettle.domain.model.Settlement;
import com.stpl.tech.kettle.domain.model.SettlementType;
import com.stpl.tech.kettle.domain.model.Subscription;
import com.stpl.tech.kettle.domain.model.SubscriptionEventItemType;
import com.stpl.tech.kettle.domain.model.SubscriptionPlanDomain;
import com.stpl.tech.kettle.domain.model.SubscriptionProduct;
import com.stpl.tech.kettle.domain.model.SubscriptionStatus;
import com.stpl.tech.kettle.domain.model.SubscriptionType;
import com.stpl.tech.kettle.domain.model.TableOrder;
import com.stpl.tech.kettle.domain.model.TableOrderItem;
import com.stpl.tech.kettle.domain.model.TableOrderItemComposition;
import com.stpl.tech.kettle.domain.model.TableStatus;
import com.stpl.tech.kettle.domain.model.TaxDetail;
import com.stpl.tech.kettle.domain.model.TransactionDetail;
import com.stpl.tech.kettle.domain.model.UnitTableMapping;
import com.stpl.tech.kettle.domain.model.WalletOrder;
import com.stpl.tech.kettle.domain.model.WalletOrderType;
import com.stpl.tech.kettle.exceptions.DataUpdationException;
import com.stpl.tech.kettle.exceptions.OfferValidationException;
import com.stpl.tech.kettle.exceptions.WebErrorCode;
import com.stpl.tech.kettle.mapper.DispenserMapper;
import com.stpl.tech.kettle.mapper.OrderInfoMapper;
import com.stpl.tech.kettle.mapper.SubscriptionPlanMapper;
import com.stpl.tech.kettle.util.RandomStringGenerator;
import com.stpl.tech.kettle.util.consumptionHelper.DesiChaiConsumptionHelper;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.PrintType;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.stpl.tech.kettle.cache.EmployeeCache;
import com.stpl.tech.kettle.cache.ProductCache;
import com.stpl.tech.kettle.cache.RecipeCache;
import com.stpl.tech.kettle.cache.UnitCacheService;
import com.stpl.tech.kettle.core.properties.EnvironmentProperties;
import com.stpl.tech.kettle.data.kettle.EmployeeMealData;
import com.stpl.tech.kettle.data.kettle.OrderDetail;
import com.stpl.tech.kettle.data.kettle.OrderExternalSettlementData;
import com.stpl.tech.kettle.data.kettle.OrderInvoiceDetail;
import com.stpl.tech.kettle.data.kettle.OrderItemAddon;
import com.stpl.tech.kettle.data.kettle.OrderItemTaxDetail;
import com.stpl.tech.kettle.data.kettle.OrderPaymentDenominationDetail;
import com.stpl.tech.kettle.data.kettle.OrderRePrintDetail;
import com.stpl.tech.kettle.data.kettle.OrderSettlement;
import com.stpl.tech.kettle.data.kettle.OrderTaxDetail;
import com.stpl.tech.kettle.data.kettle.SubscriptionDetail;
import com.stpl.tech.kettle.data.kettle.SubscriptionEventItem;
import com.stpl.tech.kettle.util.AddonData;
import com.stpl.tech.kettle.util.AppUtils;
import com.stpl.tech.kettle.util.TaxationDetailDao;
import com.stpl.tech.kettle.util.TransactionUtils;
import com.stpl.tech.kettle.util.Constants.AppConstants;
import com.stpl.tech.kettle.util.Constants.TransactionConstants;
import com.stpl.tech.master.domain.model.BillType;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductBasicDetail;
import com.stpl.tech.master.domain.model.ProductClassification;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.recipe.model.BasicInfo;
import com.stpl.tech.master.recipe.model.IngredientProductDetail;
import com.stpl.tech.master.recipe.model.IngredientVariantDetail;
import com.stpl.tech.master.recipe.model.ProductData;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import com.stpl.tech.master.recipe.model.UnitOfMeasure;

import lombok.extern.log4j.Log4j2;

import javax.annotation.Nullable;

import static com.stpl.tech.kettle.util.Constants.AppConstants.PRODUCT_SOURCE_SYSTEM_OPTION;

@Service
@Log4j2
public class OrderConverter {

	@Autowired
	private RecipeCache recipeCache;

	@Autowired
	private UnitCacheService unitCacheService;

	@Autowired
	private ProductCache productCache;

	@Autowired
	private EmployeeCache employeeCache;

	@Autowired
	private EnvironmentProperties properties;

	@Autowired
	private DispenserCanisterCache dispenserCanisterCache;

	private static final String MONTH = "MONTH";
	private static final String DAY = "DAY";

	private static final RandomStringGenerator generator = new RandomStringGenerator();

	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	public Order convert(OrderDetail obj, OrderFetchStrategy strategy) {
		long time = System.currentTimeMillis();
		Order order = new Order();
		order.setOrderId(obj.getOrderId());
		order.setRefOrderId(obj.getRefOrderId());
		order.setUnitOrderId(obj.getUnitOrderId());
		// Cannot be null
		order.setChannelPartner(obj.getChannelPartnerId());
		order.setBusinessDate(obj.getBusinessDate());
		order.setDeliveryPartner(obj.getDeliveryPartnerId());
		order.setCustomerId(obj.getCustomerId());
		// Cannot be null
		order.setEmployeeId(obj.getEmpId());
		order.setSource(obj.getOrderSource());
		order.setSourceId(obj.getOrderSourceId());
		order.setPartnerCustomerId(obj.getPartnerCustomerId());
		order.setGenerateOrderId(obj.getGeneratedOrderId());
		order.setHasParcel(convert(AppConstants.getValue(obj.getHasParcel(), AppConstants.DEFAULT_HAS_PARCEL)));
		order.setSettlementType(obj.getSettlementType() == null ? TransactionConstants.DEFAULT_SETTLEMENT_TYPE
				: SettlementType.valueOf(obj.getSettlementType()));
		order.setStatus(obj.getOrderStatus() == null ? TransactionConstants.DEFAULT_ORDER_STATUS
				: OrderStatus.valueOf(obj.getOrderStatus()));
		order.setCancellationDetails(getCancellationDetail(obj));
		// /Cannot be null
		order.setUnitId(obj.getUnitId());

		order.setUnitName(unitCacheService.getUnitBasicDetailById(obj.getUnitId()).getName());
		order.setTerminalId(obj.getTerminalId());
		order.setTableNumber(obj.getTableNumber());
		order.setTransactionDetail(
				toTransactionDetail(obj, (strategy == null || (strategy != null && strategy.isFetchTaxes()))));
		order.setBillCreationTime(obj.getBillGenerationTime());
		order.setBillStartTime(obj.getBillStartTime());
		order.setPrintCount(obj.getPrintCount());
		order.setOrderRemark(obj.getOrderRemark());
		order.setSourceVersion(obj.getSourceVersion());
		order.setDeliveryAddress(obj.getDeliveryAddress());
		order.setSubscriptionDetail(convert(obj.getSubscriptionDetail()));
		order.setOfferCode(obj.getOfferCode());
		order.setTempCode(obj.getTempCode());
		order.setBillingServerTime(obj.getBillingServerTime());
		order.setCustomerName(obj.getCustomerName() != null ? obj.getCustomerName() : "");
		order.setCampaignId(obj.getCampaignId());
		order.setTokenNumber(obj.getTokenNumber());
		order.setOrderType(obj.getOrderType());
		order.setLinkedOrderId(obj.getLinkedOrderId());
		order.setPointsRedeemed(obj.getPointsRedeemed() != null ? obj.getPointsRedeemed() : 0);
		order.setTableRequestId(obj.getTableRequestId());
		order.setGiftCardOrder(AppConstants.getValue(obj.getGiftCardOrder()));
		order.setOrderAttribute(obj.getOrderAttribute());
		order.setQrLink(obj.getQrLink());
		order.setQrHeader(TransactionConstants.QR_CODE_HEARDER);
		order.setBrandId(obj.getBrandId());
		order.setNoOfPax(obj.getNoOfPax());
		if (AppConstants.ORDER_TYPE_PAID_EMPLOYEE_MEAL.equals(obj.getOrderType())
				|| AppConstants.ORDER_TYPE_EMPLOYEE_MEAL.equals(obj.getOrderType())) {
			List<EmployeeMealData> employeeMealData = obj.getEmployeeMealData();
			if (Objects.nonNull(employeeMealData) && employeeMealData.size() > 0) {
				for (EmployeeMealData employee : employeeMealData) {
					if (order.getUnitId() == employee.getUnitId()) {
						order.setEmployeeMeal(true);
						order.setEmployeeIdForMeal(employee.getEmployeeId());
						break;
					}
				}
			}
		}
		if (obj.getManualBillBookNo() != null) {
			order.setBillBookNo(obj.getManualBillBookNo());
		}

		if ((strategy == null || (strategy != null && strategy.isFetchPrintDetails())) && obj.getPrintCount() > 1) {
			for (OrderRePrintDetail detail : obj.getOrderReprints()) {
				order.getReprints().add(convert(detail));
			}
		}
		if ((strategy == null || (strategy != null && strategy.isFetchItems()))) {
			for (com.stpl.tech.kettle.data.kettle.OrderItem item : obj.getOrderItems()) {
				if (item.getParentItemId() == null) {
					OrderItem oi = convert(item,order.getSource(),order.getUnitId());
					if (AppConstants.SERVICE_CHARGE_SUBTYPE.equals(oi.getProductSubType())) {
						log.info("Found service charge item :: {}", item.getProductId());
						order.setServiceChargeItem(oi);
						continue;
					}
					order.getOrders().add(oi);
				}
			}

			for (com.stpl.tech.kettle.data.kettle.OrderItem item : obj.getOrderItems()) {
				if (item.getParentItemId() != null) {
					OrderItem comboItem = convert(item, order.getSource(),order.getUnitId());
					for (OrderItem comboParent : order.getOrders()) {
						if (item.getParentItemId().equals(comboParent.getItemId())) {
							comboParent.getComposition().getMenuProducts().add(comboItem);
							break;
						}
					}
				}

			}

		}
		if ((strategy == null || (strategy != null && strategy.isFetchSettlement()))) {
			for (OrderSettlement item : obj.getOrderSettlements()) {
				order.getSettlements().add(convert(item));
				if (item.getPaymentModeId() == AppConstants.PAYMENT_MODE_CASH) {
					order.setPendingCash(true);
				}
			}
		}
		if (obj.getOutOfDelivery() != null && obj.getOutOfDelivery().equalsIgnoreCase("Y")) {
			order.setOod(true);
		} else {
			order.setOod(false);
		}
		if (obj.getInvoiceDetail() != null) {
			OrderInvoiceDetail invoice = obj.getInvoiceDetail();
			OrderInvoice i = new OrderInvoice();
			i.setCompanyAddress(invoice.getCompanyAddress());
			i.setCompanyName(invoice.getCompanyName());
			i.setInvoiceId(obj.getInvoiceId());
			i.setGstIn(invoice.getGstIn());
			i.setTaxType(invoice.getTaxType());
			order.setInvoice(i);
		}
		log.info("orderConverter took {}", System.currentTimeMillis() - time);
        TransactionUtils.calculateOrderPrepTime(order, productCache);
		return order;
	}

	private boolean convert(String val) {
		return val.toUpperCase().equals(AppConstants.YES);
	}

	private ActionDetail getCancellationDetail(OrderDetail order) {
		ActionDetail obj = new ActionDetail();
		obj.setActionTime(order.getBillCancellationTime());
		obj.setApprovedBy(order.getCancelApprovedBy());
		obj.setGeneratedBy(order.getCancelledBy());
		obj.setReason(order.getCancelationReason());
		obj.setBookedWastage(order.getWastageType());
		obj.setReasonId(order.getCancellationReasonId());
		return obj;
	}

	@Transactional(rollbackFor = Exception.class, value = "KettleTransactionManager", readOnly = false, propagation = Propagation.REQUIRED)
	private TransactionDetail toTransactionDetail(OrderDetail obj, boolean fetchTaxes) {
		TransactionDetail detail = new TransactionDetail();
		DiscountDetail discount = new DiscountDetail();
		discount.setDiscount(convert(obj.getDiscountPercent(), obj.getDiscountAmount()));
		discount.setDiscountCode(obj.getDiscountReasonId());
		discount.setPromotionalOffer(obj.getPromotionalDiscount());
		discount.setDiscountReason(obj.getDiscountReason());
		discount.setTotalDiscount(obj.getTotalDiscount());
		detail.setDiscountDetail(discount);
		detail.setTotalAmount(obj.getTotalAmount());
		detail.setSaleAmount(obj.getSaleAmount());
		detail.setTaxableAmount(obj.getTaxableAmount());
		detail.setPaidAmount(obj.getSettledAmount());
		detail.setRoundOffValue(obj.getRoundOffAmount());
		detail.setSavings(obj.getSavingAmount());
		detail.setTax(obj.getTaxAmount());
		detail.setServiceCharge(obj.getServiceCharge());
		detail.setServiceChargePercent(obj.getServiceChargePercent());
		if (obj.getOrderTaxes() != null && fetchTaxes) {
			for (OrderTaxDetail tax : obj.getOrderTaxes()) {
				detail.getTaxes().add(convert(tax));
			}
		}
		return detail;
	}

	private <T extends TaxationDetailDao> TaxDetail convert(T tax) {
		TaxDetail taxDetail = new TaxDetail();
		taxDetail.setCode(tax.getTaxCode());
		taxDetail.setType(tax.getTaxType());
		taxDetail.setValue(tax.getTotalTax());
		taxDetail.setPercentage(tax.getTaxPercentage());
		taxDetail.setTotal(tax.getTotalAmount());
		taxDetail.setTaxable(tax.getTaxableAmount());
		return taxDetail;
	}

	private PercentageDetail convert(BigDecimal percentage, BigDecimal value) {
		PercentageDetail detail = new PercentageDetail();
		detail.setPercentage(percentage == null ? BigDecimal.ZERO : percentage);
		detail.setValue(value == null ? BigDecimal.ZERO : value);
		return detail;
	}

	public Subscription convert(SubscriptionDetail detail) {
		if (detail == null) {
			return null;
		}

		Subscription subscription = new Subscription();
		subscription.setType(SubscriptionType.valueOf(detail.getSubscriptionType()));
		List<SubscriptionEventItem> eventList = detail.getEventItems();
		subscription.getDaysOfTheWeek().addAll(convert(SubscriptionEventItemType.DAY_OF_WEEK, eventList));
		subscription.getTimeOfTheDay().addAll(convert(SubscriptionEventItemType.TIME_OF_DAY, eventList));
		subscription.getDaysOfTheMonth().addAll(convert(SubscriptionEventItemType.DAY_OF_MONTH, eventList));
		subscription.setEndDate(AppUtils.getDate(detail.getEndDate()));
		subscription.setStartDate(AppUtils.getDate(detail.getStartDate()));
		subscription.setSubscriptionId(detail.getSubscriptionId());
		subscription.setAutomatedDelivery(AppConstants.getValue(detail.getAutomatedDelivery()));
		subscription.setEmailNotification(AppConstants.getValue(detail.getEmailNotification()));
		subscription.setSmsNotification(AppConstants.getValue(detail.getSmsNotification()));
		subscription.setSubscriptionStatus(SubscriptionStatus.valueOf(detail.getSubscriptionStatus()));

		return subscription;
	}

	private List<Integer> convert(SubscriptionEventItemType type, List<SubscriptionEventItem> events) {
		List<Integer> list = new ArrayList<>();
		if (events != null) {
			for (SubscriptionEventItem event : events) {
				if (AppConstants.ACTIVE.equals(event.getEventItemStatus())
						&& type.name().equals(event.getEventItemType())) {
					list.add(event.getEventItemValue());
				}
			}
		}
		return list;

	}

	private static ActionDetail convert(OrderRePrintDetail detail) {
		ActionDetail obj = new ActionDetail();
		obj.setReason(detail.getPrintReason());
		obj.setApprovedBy(detail.getApprovedBy());
		obj.setGeneratedBy(detail.getGeneratedBy());
		obj.setActionTime(detail.getReprintTime());
		return obj;
	}

	private IdCodeName setSubCategory(IdCodeName idCodeName, Integer subId) {
		if (Objects.isNull(idCodeName)) {
			return new IdCodeName(subId, "", "");
		}
		return idCodeName;
	}

	public OrderItem convert(com.stpl.tech.kettle.data.kettle.OrderItem item, String source,Integer unitId) {
		OrderItem data = new OrderItem();
		Product product = productCache.getProductById(item.getProductId());
		data.setItemId(item.getOrderItemId());
		data.setProductId(item.getProductId());
		if(Objects.nonNull(product))
		{
			data.setProductAttr(product.getAttribute());
			data.setProductCategory(productCache.getProductCategory(product.getType()).getDetail());
			data.setProductSubCategory(
				setSubCategory(unitCacheService.getSubcategoryById(product.getSubType()), product.getSubType()));
			data.setStationCategory(product.getStationCategoryName());
			data.setProductSubType(product.getSubType());
		}
		data.setProductName(item.getProductName());
		data.setProductAliasName(Objects.nonNull(item.getProductAliasName()) ? item.getProductAliasName() : item.getProductName());
		data.setAmount(item.getPaidAmount());
		data.setTotalAmount(item.getTotalAmount());
		data.setBillType(BillType.valueOf(item.getBillType()));
		data.setCode(item.getTaxCode());
		data.setTakeAway(AppConstants.getValue(item.getTakeAway()));
		data.setComplimentaryDetail(toComplimentaryDetail(item));
		data.setDimension(item.getDimension());
		DiscountDetail discount = new DiscountDetail();
		discount.setDiscount(convert(item.getDiscountPercent(), item.getDiscountAmount()));
		discount.setDiscountCode(item.getDiscountReasonId());
		discount.setDiscountReason(item.getDiscountReason());
		discount.setPromotionalOffer(item.getPromotionalDiscount());
		data.setDiscountDetail(discount);
		data.setPrice(item.getPrice());
		data.setQuantity(item.getQuantity());
		data.setComposition(new OrderItemComposition());
		data.setRecipeId(item.getRecipeId() != null ? item.getRecipeId() : 0);
		data.setRecipeProfile(item.getRecipeProfile());
		data.setTaxDeductedByPartner(AppUtils.getStatus(item.getTaxDeductedByPartner()));
		data.setOrderItemRemark(item.getOrderItemRemark());
		data.setProductDescription(product.getDescription());
		// add Others to Composition
		addOthersToComposition(item, data, source);
		// TODO add required variable
		// ProductSource.SCM, ProductClassification.VARIANT
		if (true) {
			for (OrderItemAddon addon : item.getOrderItemAddons()) {
				addToComposition(addon, data);
			}
		}
		try {
			Unit unit = unitCacheService.getUnitById(unitId);
			if (AppUtils.getStatus(unit.getIsDispenserEnabled())) {
				setDispenserShotsInfoToOrderItem(item, data);
			}
		} catch (Exception e) {
			log.error("Error while setting dispenser shots info to order item : {}", item.getOrderItemId(), e);
		}
		data.setTax(item.getTaxAmount());
		if (item.getOrderItemTaxes() != null) {
			for (OrderItemTaxDetail tax : item.getOrderItemTaxes()) {
				TaxDetail taxDetail = convert(tax);
				if(Objects.nonNull(tax.getOrderItemTaxDetailId()) && tax.getOrderItemTaxDetailId()!=0) {
					taxDetail.setTaxId(tax.getOrderItemTaxDetailId());
				}
				data.getTaxes().add(taxDetail);
			}
		}
		return data;
	}

	private ComplimentaryDetail toComplimentaryDetail(com.stpl.tech.kettle.data.kettle.OrderItem item) {
		ComplimentaryDetail data = new ComplimentaryDetail();
		data.setIsComplimentary(
				convert(AppConstants.getValue(item.getIsComplimentary(), AppConstants.DEFAULT_IS_COMPLIMENTARY)));
		data.setReason(item.getComplimentaryReason());
		data.setReasonCode(item.getComplimentaryTypeId());
		return data;
	}

	private void addOthersToComposition(com.stpl.tech.kettle.data.kettle.OrderItem item, OrderItem data,
										String source) {
		RecipeDetail recipeDetail = recipeCache.getRecipeByRecipeId(item.getRecipeId());
		if (Objects.nonNull(recipeDetail)) {
			if (Objects.nonNull(source)) {
				switch (UnitCategory.valueOf(source)) {
				case COD:
					if (Objects.nonNull(recipeDetail.getDeliveryConsumables())
							&& recipeDetail.getDeliveryConsumables().size() > 0) {
						recipeDetail.getDeliveryConsumables().stream().forEach(deliveryConsumable -> {
							if (Objects.nonNull(deliveryConsumable.getDisplay()) && deliveryConsumable.getDisplay()
									&& Objects.nonNull(deliveryConsumable.getDisplayCode())) {
								data.getComposition().getOthers().add(deliveryConsumable);
							}
						});
					}
					break;
				case CAFE:
					if (Objects.nonNull(recipeDetail.getDineInConsumables())
							&& recipeDetail.getDineInConsumables().size() > 0) {
						recipeDetail.getDineInConsumables().stream().forEach(dineInConsumable -> {
							if (Objects.nonNull(dineInConsumable.getDisplay()) && dineInConsumable.getDisplay()
									&& Objects.nonNull(dineInConsumable.getDisplayCode())) {
								data.getComposition().getOthers().add(dineInConsumable);
							}
						});
					}
					break;
				case TAKE_AWAY:
					if (Objects.nonNull(recipeDetail.getTakeawayConsumables())
							&& recipeDetail.getTakeawayConsumables().size() > 0) {
						recipeDetail.getTakeawayConsumables().stream().forEach(takewayConsumable -> {
							if (Objects.nonNull(takewayConsumable.getDisplay()) && takewayConsumable.getDisplay()
									&& Objects.nonNull(takewayConsumable.getDisplayCode())) {
								data.getComposition().getOthers().add(takewayConsumable);
							}
						});
					}
					break;
				default:
					break;
				}
			}
			if (Objects.nonNull(recipeDetail.getIngredient())
					&& Objects.nonNull(recipeDetail.getIngredient().getComponents())
					&& recipeDetail.getIngredient().getComponents().size() > 0) {
				recipeDetail.getIngredient().getComponents().stream().forEach(otherProduct -> {
					if (Objects.nonNull(otherProduct.getDisplay()) && otherProduct.getDisplay()
							&& Objects.nonNull(otherProduct.getDisplayCode())) {
						data.getComposition().getOthers().add(otherProduct);
					}
				});
			}
		}
	}

	private void addToComposition(AddonData addon, OrderItem data) {
		if (addon.getType() != null) {
			if (!addon.getType().equalsIgnoreCase(AppConstants.PREFERENCE)) {
				switch (ProductClassification.valueOf(addon.getType())) {
				case VARIANT:
					data.getComposition().getVariants().add(convertToVarient(addon)); // IngredientVarientDetail
					break;
				case PRODUCT_VARIANT:
					data.getComposition().getProducts().add(convert(addon)); // IngredientProductDetail
					break;
				case FREE_ADDON:
					data.getComposition().getAddons().add(convert(addon)); // IngredientProductDetail
					break;
				case PAID_ADDON:
					data.getComposition().getAddons().add(convert(addon)); // IngredientProductDetail
					break;
				case FREE_OPTION:
					if(Objects.nonNull(productCache.getSpecialMilkVariant(addon.getName()))){
						data.setMilkVariant(MilkVariant.builder().productId(productCache.getSpecialMilkVariant(addon.getName())).
								productName(addon.getName()).profile(AppUtils.getMilkVariantPaidAddonPrefix(addon.getName()) + data.getRecipeProfile())
								.build());
					}
					data.getComposition().getOptions().add(addon.getName()); // String
					break;
				/*
				 * case OTHERS: data.getComposition().getOthers().add(convert(cache,addon));
				 */
				default:
					break;
				}
			}
		} else {
			data.getComposition().getAddons().add(convert(addon));
		}
	}

	private IngredientVariantDetail convertToVarient(AddonData addon) {
		IngredientVariantDetail i = new IngredientVariantDetail();
		i.setDefaultSetting(AppConstants.getValue(addon.getDefaultSetting()));
		i.setQuantity(addon.getQuantity());
		i.setUom(addon.getUom() == null ? null : UnitOfMeasure.valueOf(addon.getUom()));
		i.setId(addon.getProductId());
		i.setAlias(addon.getName());
		i.setName(addon.getName());
		if(Objects.nonNull(addon.getOrderItemAddonId()) && addon.getOrderItemAddonId()!=0){
			i.setIngredientVariantDetailId(addon.getOrderItemAddonId());
		}
		return i;
	}

	private IngredientProductDetail convert(AddonData addon) {
		IngredientProductDetail i = new IngredientProductDetail();
		i.setQuantity(addon.getQuantity());
		i.setDefaultSetting(AppConstants.getValue(addon.getDefaultSetting()));
		i.setUom(addon.getUom() == null ? null : UnitOfMeasure.valueOf(addon.getUom()));
		ProductData p = new ProductData();
		if (ProductSource.MENU.name().equals(addon.getSource())) {
			ProductBasicDetail detail = productCache.getProductBasicDetailById(addon.getProductId());
			if (detail != null) {
				p.setClassification(detail.getClassification());
				p.setCode(detail.getDetail().getCode());
				p.setName(detail.getDetail().getName());
				p.setProductId(detail.getDetail().getId());
				p.setShortCode(detail.getDetail().getShortCode());
			}

		} else {
			p.setProductId(addon.getProductId());
			p.setName(addon.getName());
		}
		i.setProduct(p);
		if(Objects.nonNull(addon.getOrderItemAddonId()) && addon.getOrderItemAddonId()!=0){
			i.setIngredientProductDetailId(addon.getOrderItemAddonId());
		}
		BasicInfo info = new BasicInfo();
		info.setCode(addon.getDimension() != null ? addon.getDimension() : AppConstants.DIMENSION_NONE);
		info.setName(addon.getDimension() != null ? addon.getDimension() : AppConstants.DIMENSION_NONE);
		i.setDimension(info);
		return i;
	}

	public Settlement convert(OrderSettlement settlement) {

		Settlement data = new Settlement();
		data.setAmount(settlement.getAmountPaid());
		data.setMode(settlement.getPaymentModeId());
		data.setSettlementId(settlement.getSettlementId());
		data.setModeDetail(unitCacheService.getPaymentModeById(settlement.getPaymentModeId()));
		if (settlement.getExternalTransactions() != null && settlement.getExternalTransactions().size() > 0) {
			for (OrderExternalSettlementData d : settlement.getExternalTransactions()) {
				ExternalSettlement s = new ExternalSettlement();
				s.setExternalSettlementId(d.getExternalSettlementId());
				s.setExternalTransactionId(d.getExternalTransactionId());
				s.setAmount(d.getAmountPaid());
				data.getExternalSettlements().add(s);
			}
		}
		if (settlement.getPreviousPaymentMode() != null) {
			data.setEdited(AppConstants.getValue(settlement.getEdited()));
			data.setOldMode(unitCacheService.getPaymentModeById(settlement.getPreviousPaymentMode()));
			data.setEditedBy(employeeCache.getEmployeeNameById(settlement.getEditedBy())); // TODO
		}
		if (settlement.getExtraVouchers().compareTo(BigDecimal.ZERO) == 0) {
			data.setExtraVouchers(BigDecimal.ZERO);
		} else {
			data.setExtraVouchers(settlement.getExtraVouchers());
		}
		for (OrderPaymentDenominationDetail denomination : settlement.getDenominations()) {
			data.getDenominations().add(convert(denomination));
		}
		return data;

	}

	private OrderPaymentDenomination convert(OrderPaymentDenominationDetail denomination) {
		OrderPaymentDenomination data = new OrderPaymentDenomination();
		data.setId(denomination.getId());
		data.setDenominationDetailId(denomination.getId());
		data.setOrderId(denomination.getOrderId());
		data.setTotalAmount(data.getTotalAmount());
		data.setSettlementId(denomination.getOrderSettlement().getSettlementId());
		data.setCount(data.getCount());
		return data;
	}

	public Order createRepunchOrderOfZeroAmount(Order order, List<Integer> orderItemIds, boolean isComplimentary , @Nullable int complimentaryCode ,@Nullable String orderType) throws OfferValidationException {
		Order rePuchOrder = SerializationUtils.clone(order);
		List<OrderItem> removeItems = new ArrayList<>();
		DiscountDetail discountDetail = new DiscountDetail();
		Settlement  settlement = new Settlement();
		settlement.setAmount(BigDecimal.ZERO);
		settlement.setMode(AppConstants.CASH_SETTLEMENT_MODE);
		for(OrderItem item : rePuchOrder.getOrders()){
			if(!orderItemIds.contains(item.getItemId())){
				removeItems.add(item);
			}else{
				if(!AppConstants.ATM_UNSTSFIED_CUST_ELIG_PROD_TYPE.contains(item.getProductCategory().getId())){
					throw new OfferValidationException(item.getProductName() + " Not Allowed for unsatisfied customer order" , WebErrorCode.PRODUCT_NOT_FOUND);
				}
				if(isComplimentary) {
					ComplimentaryDetail detail = item.getComplimentaryDetail();
					detail.setIsComplimentary(true);
					detail.setReasonCode(complimentaryCode);
				}
				item.setItemId(0);
				item.setAmount(BigDecimal.ZERO);
				item.setDiscountDetail(discountDetail);
				item.setTotalAmount(BigDecimal.ZERO);
				item.setTax(BigDecimal.ZERO);
				item.getTaxes().forEach(tax->{
					tax.setValue(BigDecimal.ZERO);
					tax.setTotal(BigDecimal.ZERO);
					tax.setTaxable(BigDecimal.ZERO);
				});
			}
		}
		rePuchOrder.getOrders().removeAll(removeItems);
		TransactionDetail transactionDetail = rePuchOrder.getTransactionDetail();
		transactionDetail.setTotalAmount(BigDecimal.ZERO);
		transactionDetail.setTaxableAmount(BigDecimal.ZERO);
		transactionDetail.setSavings(BigDecimal.ZERO);
		transactionDetail.setDiscountDetail(discountDetail);
		transactionDetail.setPaidAmount(BigDecimal.ZERO);
		transactionDetail.setRoundOffValue(BigDecimal.ZERO);
		transactionDetail.setTax(BigDecimal.ZERO);
		transactionDetail.getTaxes().forEach(tax->{
			tax.setValue(BigDecimal.ZERO);
			tax.setTotal(BigDecimal.ZERO);
			tax.setTaxable(BigDecimal.ZERO);
		});
		rePuchOrder.setSettlementType(SettlementType.DEBIT);
		rePuchOrder.setSettlements(List.of(settlement));
		rePuchOrder.setBillStartTime(new Date());
		rePuchOrder.setBypassLoyateaAward(true);
		rePuchOrder.setOrderType(orderType);
		rePuchOrder.setStatus(OrderStatus.CREATED);
		rePuchOrder.setCashRedeemed(BigDecimal.ZERO);
		rePuchOrder.setLinkedOrderId(order.getOrderId());
		rePuchOrder.setGenerateOrderId(order.getGenerateOrderId());//to prevent duplicate
		rePuchOrder.setOrderId(null);
		return rePuchOrder;
	}

	public Order extractWalletOrder(Order order, WalletOrderType type, WalletOrder wallet) {
		if(order.getOrderType().equals(AppConstants.ORDER_TYPE_COMPLIMENTARY_GIFTCARD)){
			return extractWalletOrderForWA(order, type, wallet);
		}
		Order walletOrder = SerializationUtils.clone(order);
		Product product = null;
		OrderItem item = new OrderItem();
		List<OrderItem> orders = new ArrayList<>();
		orders.add(item);
		if (WalletOrderType.DIRECT.equals(type)) {
			product = productCache.getProductById(properties.getDirectWalletId());
			item.setCardType(CashCardType.AP01.name());
		} else if (WalletOrderType.MICRO.equals(type)) {
			product = productCache.getProductById(properties.getMicroWalletId());
			item.setCardType(CashCardType.ECARD.name());
		}
		if (Objects.nonNull(product)) {
			BigDecimal amount = BigDecimal.ZERO;
			if (Objects.nonNull(wallet.getSuggestedAmount())
					&& wallet.getSuggestedAmount().compareTo(BigDecimal.ZERO) != 0) {
				amount = wallet.getSuggestedAmount();
			}
			if (Objects.nonNull(wallet.getRechargeAmount())
					&& wallet.getRechargeAmount().compareTo(BigDecimal.ZERO) != 0) {
				amount = amount.add(wallet.getRechargeAmount());
			}
			walletOrder.setSource(AppConstants.CAFE);
			if(AppUtils.isDineInOrAppOrder(order.getChannelPartner())){
				walletOrder.setChannelPartner(order.getChannelPartner());
			}else {
				walletOrder.setChannelPartner(AppConstants.DINE_IN_CHANNEL_PARTNER);
			}
			walletOrder.setHasParcel(false);
			walletOrder.setAwardLoyalty(false);
			walletOrder.setPointsRedeemed(0);
			walletOrder.setOfferCode(null);
			walletOrder.setContainsSignupOffer(false);
			item.setItemId(1);
			item.setProductId(product.getId());
			item.setProductName(product.getName());
			item.setProductAliasName(Objects.nonNull(product.getProductAliasName()) ? product.getProductAliasName() : product.getName());
			BigDecimal price = null;
			if (!CollectionUtils.isEmpty(product.getPrices())) {
				price = product.getPrices().get(0).getPrice();
			} else {
				price = BigDecimal.ONE;// TODO pick from cache
			}
			item.setPrice(price);
			item.setQuantity(amount.divide(price).intValue());
			BigDecimal offerAmount = BigDecimal.ZERO;
			if (Objects.nonNull(wallet.getOfferAmount()) && wallet.getOfferAmount().compareTo(BigDecimal.ZERO) != 0) {
				offerAmount = wallet.getOfferAmount();
			}
			if (Objects.nonNull(wallet.getExtraAmount()) && wallet.getExtraAmount().compareTo(BigDecimal.ZERO) != 0) {
				offerAmount = offerAmount.add(wallet.getExtraAmount());
			}
			if (offerAmount.compareTo(BigDecimal.ZERO) != 0) {
				item.setOfferAmount(offerAmount);
			}
			item.setTotalAmount(amount);
			item.setAmount(amount);
			item.setDimension(AppConstants.DIMENSION_NONE);
			item.setBillType(product.getBillType());
			item.setCode(product.getTaxCode());
			item.setTax(BigDecimal.ZERO);
			ComplimentaryDetail complimentaryDetail = new ComplimentaryDetail();
			complimentaryDetail.setIsComplimentary(false);
			item.setComplimentaryDetail(complimentaryDetail);
			walletOrder.setOrders(orders);
			TransactionDetail transactionDetail = new TransactionDetail();
			DiscountDetail discountDetail = new DiscountDetail();
			discountDetail.setTotalDiscount(BigDecimal.ZERO);
			PercentageDetail percentageDetail = new PercentageDetail();
			percentageDetail.setPercentage(BigDecimal.ZERO);
			percentageDetail.setValue(BigDecimal.ZERO);
			discountDetail.setDiscount(percentageDetail);
			discountDetail.setPromotionalOffer(BigDecimal.ZERO);
			transactionDetail.setTotalAmount(amount);
			transactionDetail.setTaxableAmount(amount);
			transactionDetail.setDiscountDetail(discountDetail);
			transactionDetail.setPaidAmount(amount);
			transactionDetail.setRoundOffValue(BigDecimal.ZERO);
			transactionDetail.setSavings(BigDecimal.ZERO);
			transactionDetail.setTax(BigDecimal.ZERO);
			walletOrder.setTransactionDetail(transactionDetail);
			if (wallet.isDeductFromWallet()) {
				if (Objects.isNull(order.getSettlements())) {
					order.setSettlements(new ArrayList());
				}
				Settlement settlement = new Settlement();
				settlement.setExternalSettlements(new ArrayList());
				settlement.setMode(properties.getCashCardPaymentModeId());
				settlement.setAmount(order.getTransactionDetail().getPaidAmount());
				order.getSettlements().clear();
				order.getSettlements().add(settlement);
			}
		}
		walletOrder.setCashCardExtraAmt(item.getOfferAmount());
		walletOrder.setCombinedOrder(true);
		return walletOrder;
	}

	public Order extractWalletOrderForWA(Order order, WalletOrderType type, WalletOrder wallet) {
		Order walletOrder = SerializationUtils.clone(order);
		Product product = null;
		OrderItem item = new OrderItem();
		List<OrderItem> orders = new ArrayList<>();
		orders.add(item);
		if (WalletOrderType.DIRECT.equals(type)) {
			product = productCache.getProductById(properties.getDirectWalletId());
			item.setCardType(CashCardType.AP01.name());
		} else if (WalletOrderType.MICRO.equals(type)) {
			product = productCache.getProductById(properties.getMicroWalletId());
			item.setCardType(CashCardType.ECARD.name());
		}
		if (Objects.nonNull(product)) {
			BigDecimal amount = BigDecimal.ZERO;
			if (Objects.nonNull(wallet.getSuggestedAmount())
					&& wallet.getSuggestedAmount().compareTo(BigDecimal.ZERO) != 0) {
				amount = wallet.getSuggestedAmount();
			}
			if (Objects.nonNull(wallet.getRechargeAmount())
					&& wallet.getRechargeAmount().compareTo(BigDecimal.ZERO) != 0) {
				amount = amount.add(wallet.getRechargeAmount());
			}
			walletOrder.setSource(AppConstants.CAFE);
			walletOrder.setChannelPartner(AppConstants.DINE_IN_CHANNEL_PARTNER);
			walletOrder.setHasParcel(false);
			walletOrder.setAwardLoyalty(false);
			walletOrder.setPointsRedeemed(0);
			walletOrder.setOfferCode(order.getOfferCode());
			walletOrder.setContainsSignupOffer(false);
			item.setItemId(1);
			item.setProductId(product.getId());
			item.setProductName(product.getName());
			item.setProductAliasName(Objects.nonNull(product.getProductAliasName()) ? product.getProductAliasName() : product.getName());
			BigDecimal price = null;
			if (!CollectionUtils.isEmpty(product.getPrices())) {
				price = product.getPrices().get(0).getPrice();
			} else {
				price = BigDecimal.ONE;// TODO pick from cache
			}
			item.setPrice(price);
			item.setQuantity(amount.divide(price).intValue());
			BigDecimal offerAmount = BigDecimal.ZERO;
			if (Objects.nonNull(wallet.getOfferAmount()) && wallet.getOfferAmount().compareTo(BigDecimal.ZERO) != 0) {
				offerAmount = wallet.getOfferAmount();
			}
			if (Objects.nonNull(wallet.getExtraAmount()) && wallet.getExtraAmount().compareTo(BigDecimal.ZERO) != 0) {
				offerAmount = offerAmount.add(wallet.getExtraAmount());
			}
			if (offerAmount.compareTo(BigDecimal.ZERO) != 0) {
				item.setOfferAmount(offerAmount);
			}
			item.setTotalAmount(amount);
			item.setAmount(amount);
			item.setDimension(AppConstants.DIMENSION_NONE);
			item.setBillType(product.getBillType());
			item.setCode(product.getTaxCode());
			item.setTax(BigDecimal.ZERO);
			ComplimentaryDetail complimentaryDetail = new ComplimentaryDetail();
			complimentaryDetail.setIsComplimentary(false);
			item.setComplimentaryDetail(complimentaryDetail);
			walletOrder.setOrders(orders);
			TransactionDetail transactionDetail = new TransactionDetail();
			DiscountDetail discountDetail = order.getTransactionDetail().getDiscountDetail();
			transactionDetail.setTotalAmount(order.getTransactionDetail().getTotalAmount());
			transactionDetail.setTaxableAmount(order.getTransactionDetail().getTaxableAmount());
			transactionDetail.setDiscountDetail(discountDetail);
			transactionDetail.setPaidAmount(order.getTransactionDetail().getPaidAmount());
			transactionDetail.setRoundOffValue(BigDecimal.ZERO);
			transactionDetail.setSavings(BigDecimal.ZERO);
			transactionDetail.setTax(BigDecimal.ZERO);
			walletOrder.setTransactionDetail(transactionDetail);
			if (wallet.isDeductFromWallet()) {
				if (Objects.isNull(order.getSettlements())) {
					order.setSettlements(new ArrayList());
				}
				Settlement settlement = new Settlement();
				settlement.setExternalSettlements(new ArrayList());
				settlement.setMode(properties.getCashCardPaymentModeId());
				settlement.setAmount(order.getTransactionDetail().getPaidAmount());
				order.getSettlements().clear();
				order.getSettlements().add(settlement);
			}
		}
		walletOrder.setCashCardExtraAmt(item.getOfferAmount());
		walletOrder.setCombinedOrder(true);
		return walletOrder;
	}

	public OrderDomain getOrderDomain(CustomerInfo customerInfo, Integer walletAmount, Integer extraAmount){
		OrderDomain orderDomain = new OrderDomain();
		WalletOrder walletOrder = new WalletOrder();
		Order order = new Order();
		String randomString = "WA"+String.valueOf(customerInfo.getCustomerId())+ generator.getRandomCode(5);
		order.setGenerateOrderId(randomString);
		order.setEmployeeId(120056);
		order.setHasParcel(false);
		order.setStatus(OrderStatus.CREATED);
		order.setOrders(new ArrayList<>());
		TransactionDetail transactionDetail = new TransactionDetail();
		DiscountDetail discountDetail = new DiscountDetail();
		transactionDetail.setTotalAmount(BigDecimal.valueOf(walletAmount));
		transactionDetail.setTaxableAmount(BigDecimal.ZERO);
		transactionDetail.setPaidAmount(BigDecimal.ZERO);
		transactionDetail.setSavings(BigDecimal.ZERO);
		transactionDetail.setTax(BigDecimal.ZERO);
		transactionDetail.setRoundOffValue(BigDecimal.ZERO);
		transactionDetail.getTaxes();
		PercentageDetail percentageDetail = new PercentageDetail();
		percentageDetail.setPercentage(BigDecimal.valueOf(100));
		percentageDetail.setValue(BigDecimal.valueOf(walletAmount));
		discountDetail.setDiscount(percentageDetail);
		discountDetail.setDiscountReason(properties.getLoyaltWalletOfferCode());
		discountDetail.setTotalDiscount(BigDecimal.valueOf(walletAmount));
		discountDetail.setPromotionalOffer(BigDecimal.ZERO);
		transactionDetail.setDiscountDetail(discountDetail);
		order.setTransactionDetail(transactionDetail);
		order.setSettlementType(SettlementType.DEBIT);
		order.setSource(AppConstants.CAFE);
		Settlement settlement = new Settlement();
		settlement.setMode(AppConstants.CASH_SETTLEMENT_MODE);
		settlement.setAmount(BigDecimal.ZERO);
		settlement.setExternalSettlements(new ArrayList<>());
		order.setSettlements(Arrays.asList(settlement));
		order.setUnitId(26254);
		order.setBillStartTime(AppUtils.getCurrentTimestamp());
		order.setBillCreationSeconds(22);
		order.setBillCreationTime(AppUtils.getCurrentTimestamp());
		order.setBillingServerTime(AppUtils.getCurrentTimestamp());
		order.setChannelPartner(1);
		order.setPointsRedeemed(0);
		order.setTerminalId(1);
		order.setAwardLoyalty(false);
		order.setCustomerId(customerInfo.getCustomerId());
		order.setCustomerName(customerInfo.getFirstName());
		order.setEnquiryItems(new ArrayList<>());
		order.setMetadataList(new ArrayList<>());
		order.setContainsSignupOffer(false);
		order.setNewCustomer(false);
		order.setOrderType(AppConstants.ORDER_TYPE_COMPLIMENTARY_GIFTCARD);
		order.setCashRedeemed(BigDecimal.ZERO);
		order.setBrandId(1);
		order.setGiftCardOrder(false);
		order.setOfferCode(properties.getLoyaltWalletOfferCode());
		// add isChaayosSelectOrder to false;
		orderDomain.setOrder(order);
		walletOrder.setDeductFromWallet(false);
		walletOrder.setRechargeAmount(BigDecimal.valueOf(walletAmount));
		walletOrder.setOfferAmount(BigDecimal.ZERO);
		walletOrder.setSuggestedAmount(BigDecimal.ZERO);
		walletOrder.setExtraAmount(BigDecimal.valueOf(extraAmount));
		orderDomain.setWalletOrder(walletOrder);
		orderDomain.setIncludeReceipts(false);
		return orderDomain;
	}

	public Order extractSelectOrder(Order order) {
		Order selectOrder = SerializationUtils.clone(order);
		OrderItem subscription = null;
		if (!CollectionUtils.isEmpty(order.getOrders())) {
			for (int i = 0; i < order.getOrders().size(); i++) {
				OrderItem item = order.getOrders().get(i);
				if (productCache.getSubscriptionProductDetailsById(item.getProductId()).isPresent()) {
					subscription = item;
					order.getOrders().remove(i);
					break;
				}
			}
		}
		if (Objects.nonNull(subscription)) {
			selectOrder.setAwardLoyalty(false);
			selectOrder.setSource(AppConstants.CAFE);
			selectOrder.setHasParcel(false);
			selectOrder.setChannelPartner(AppConstants.DINE_IN_CHANNEL_PARTNER);
			selectOrder.getOrders().clear();
			selectOrder.getOrders().add(subscription);
			selectOrder.setPointsRedeemed(0);
			selectOrder.setOfferCode(null);
			selectOrder.setContainsSignupOffer(false);
			TransactionDetail transactionDetail = new TransactionDetail();
			DiscountDetail discountDetail = new DiscountDetail();
			transactionDetail.setTotalAmount(subscription.getAmount());
			transactionDetail.setTaxableAmount(subscription.getAmount());
			transactionDetail.setDiscountDetail(discountDetail);
			transactionDetail.setPaidAmount(subscription.getTotalAmount());
			transactionDetail.setSavings(BigDecimal.ZERO);
			transactionDetail.setTax(BigDecimal.ZERO);
			BigDecimal amount = BigDecimal.ZERO;
			transactionDetail.setTaxes(subscription.getTaxes());
			for (TaxDetail tax : transactionDetail.getTaxes()) {
				amount = amount.add(subscription.getTotalAmount().multiply(tax.getPercentage())
						.divide(BigDecimal.valueOf(100), 0, RoundingMode.HALF_UP));
				transactionDetail.getTax().add(tax.getValue());
			}
			amount = amount.setScale(0, RoundingMode.HALF_UP).add(subscription.getTotalAmount());
			transactionDetail.setPaidAmount(amount);
			transactionDetail.setRoundOffValue(amount.subtract(transactionDetail.getPaidAmount()));
			selectOrder.setTransactionDetail(transactionDetail);
			List<Settlement> subscriptionSettlements = new ArrayList<>();
			List<Settlement> removableSettlements = new ArrayList<>();
			for (Settlement orderSettlement : order.getSettlements()) {
				if (amount.compareTo(BigDecimal.ZERO) != 0) {
					Settlement settlement = SerializationUtils.clone(orderSettlement);
					if (orderSettlement.getAmount().compareTo(amount) >= 0) {
						settlement.setAmount(amount);
						orderSettlement.setAmount(orderSettlement.getAmount().subtract(amount));
						amount = BigDecimal.ZERO;
					} else {
						settlement.setAmount(orderSettlement.getAmount());
						removableSettlements.add(orderSettlement);
						amount = amount.subtract(orderSettlement.getAmount());
					}
					subscriptionSettlements.add(settlement);
				}
			}
			order.getSettlements().removeAll(removableSettlements);
			selectOrder.setSettlements(subscriptionSettlements);
			TransactionDetail orderTransaction = order.getTransactionDetail();
			orderTransaction.setTotalAmount(orderTransaction.getTotalAmount().subtract(transactionDetail.getTotalAmount()));
			orderTransaction
					.setTaxableAmount(orderTransaction.getTaxableAmount().subtract(transactionDetail.getTaxableAmount()));
			orderTransaction.setPaidAmount(orderTransaction.getPaidAmount().subtract(transactionDetail.getPaidAmount()));
			orderTransaction.setTax(orderTransaction.getTax().subtract(transactionDetail.getTax()));
			Set<TaxDetail> removableTaxes = new HashSet();
			for (TaxDetail subscriptionTax : transactionDetail.getTaxes()) {
				for (TaxDetail tax : orderTransaction.getTaxes()) {
					if (subscriptionTax.getType().equals(tax.getType()) && subscriptionTax.getCode().equals(tax.getCode())
							&& subscriptionTax.getPercentage().compareTo(tax.getPercentage()) == 0) {
						tax.setTaxable(tax.getTaxable().subtract(subscriptionTax.getTaxable()));
						tax.setTotal(tax.getTotal().subtract(subscriptionTax.getTotal()));
						tax.setValue(tax.getValue().subtract(subscriptionTax.getValue()));
						if (tax.getTotal().compareTo(BigDecimal.ZERO) == 0) {
							removableTaxes.add(tax);
						}
					}
				}
			}
			orderTransaction.getTaxes().removeAll(removableTaxes);
			selectOrder.setOfferCode(null);
			selectOrder.setCombinedOrder(true);
		}
		return selectOrder;
	}

	public SubscriptionProduct convert(OrderItem item, Product prd) {
		SubscriptionProduct product = new SubscriptionProduct();
		product.setDimensionCode(item.getDimension());
		product.setPrice(item.getPrice());
		product.setProductId(item.getProductId());
		product.setProductName(item.getProductName());
		product.setSubscriptionCode(prd.getSkuCode());
		product.setValidityInDays(getValidityDaysInDays(item.getDimension()));
		return product;
	}

	public int getValidityDaysInDays(String dimensionCode) {
		int months = 1;
		int days = 45;
		String addType = MONTH;
		if (dimensionCode.toLowerCase().contains("month")) {
			int endIndex = dimensionCode.toLowerCase().indexOf("month");
			String monthString = dimensionCode.substring(0, endIndex);
			months = Integer.parseInt(monthString);
		} else if (dimensionCode.toLowerCase().contains("day")) {
			addType = DAY;
			String[] str = dimensionCode.toLowerCase().split("day");
			days = Integer.parseInt(str[0]);
		}
		Date today = AppUtils.getBusinessDate();
		Date later = MONTH.equals(addType) ? AppUtils.addMonthsInDate(today, months) : AppUtils.addDays(today, days);
		return AppUtils.getDaysDiff(later, today);
	}

	public OrderInfoDomain converToOrderInfoDomain(OrderInfo orderInfo){
		OrderInfoDomain domain =  OrderInfoMapper.INSTANCE.toDomain(orderInfo);
		domain.setSubscriptionPlan(new SubscriptionPlanDomain());
		if(MapUtils.isNotEmpty(orderInfo.getOrderNotificationMap())){
			for(Map.Entry<Integer, OrderNotification> entry :orderInfo.getOrderNotificationMap().entrySet()){
				OrderNotification orderNotification = entry.getValue();
				if(Objects.nonNull(orderNotification) && StringUtils.isNotBlank(orderNotification.getPlanEndDate())){
					domain.getSubscriptionPlan().setPlanEndDate(AppUtils.getDate(orderNotification.getPlanEndDate(),"yyyy-MM-dd"));
					if(orderNotification.getChaiLeft() != 0){
						domain.getSubscriptionPlan().setChaiLeft(orderNotification.getChaiLeft());
					}
					break;
				}
			}
		}
		return domain;
	}

    public UnitTableMapping convert(UnitTableMappingDetail data){
        UnitTableMapping table = new UnitTableMapping(data.getTableRequestId(), data.getUnitId(), data.getTableNumber(),
                data.getCustomerId(), data.getCustomerName(), data.getTotalOrders(), data.getTotalAmount(),
                TableStatus.OCCUPIED, data.getContact() , data.getNoOfPax(),data.getCustomerType(),data.getSettledOrderId(),data.getServiceChargeApplied() == null || AppUtils.getStatus(data.getServiceChargeApplied()),
				data.getBillPrintAllowed() == null || AppUtils.getStatus(data.getBillPrintAllowed()),data.getOfferCode(),data.getPointsRedeemed());
        if (data.getOrders() != null) {
            for (TableOrderMappingDetail map : data.getOrders()) {
                table.getOrders().add(convert(map));
            }
        }
        return table;
    }

	public UnitTableMapping  convert(UnitTableMappingDetail data, Map<Integer, OrderItemStatus> orderItemStatusMap){
		TableStatus status = TableStatus.OCCUPIED;
		if(TableStatus.SETTLEMENT_PENDING.name().equals(data.getTableStatus())){
			status = TableStatus.SETTLEMENT_PENDING;
		}
		UnitTableMapping table = new UnitTableMapping(data.getTableRequestId(), data.getUnitId(), data.getTableNumber(),
				data.getCustomerId(), data.getCustomerName(), data.getTotalOrders(), data.getTotalAmount(),
				status, data.getContact() , data.getNoOfPax(),data.getCustomerType(),data.getSettledOrderId(),data.getServiceChargeApplied() == null || AppUtils.getStatus(data.getServiceChargeApplied()),data.getBillPrintAllowed() == null || AppUtils.getStatus(data.getBillPrintAllowed()),data.getOfferCode(),data.getPointsRedeemed());
		if (data.getOrders() != null) {
			for (TableOrderMappingDetail map : data.getOrders()) {
				table.getOrders().add(convert(map,orderItemStatusMap));
			}
		}
		return table;
	}

    public TableOrder convert(TableOrderMappingDetail map) {
        TableOrder order = new TableOrder();
        OrderDetail od = map.getOrder();
        order.setBillingServerTime(od.getBillingServerTime());
        order.setCustomerName(od.getCustomerName());
        order.setGenerateOrderId(od.getGeneratedOrderId());
        order.setOfferCode(od.getOfferCode());
        order.setOrderId(od.getOrderId());
        order.setSource(od.getOrderSource());
        order.setStatus(OrderStatus.valueOf(od.getOrderStatus()));
        order.setTransactionDetail(toTransactionDetail(od, false));
        for (com.stpl.tech.kettle.data.kettle.OrderItem item : od.getOrderItems()) {
                order.getItems().add(convert(item));

        }
        return order;
    }

	private void addTableOrderItems(com.stpl.tech.kettle.data.kettle.OrderItem item,
									Map<Integer,OrderItemStatus> orderItemStatusMap
	, Map<Integer,List<TableOrderItem>> comboItemsMap, TableOrder order, Boolean removeAfterInsertion,
									TableOrderItemComposition tableOrderItemComposition) {

		if(Objects.isNull(item.getParentItemId())) {
			TableOrderItem tableOrderItem = convert(item, orderItemStatusMap.get(item.getOrderItemId()));
			if (tableOrderItemComposition != null) {
				tableOrderItem.setOrderItemComposition(tableOrderItemComposition);
			}
			order.getItems().add(tableOrderItem);
		} else {
			if(!comboItemsMap.containsKey(item.getParentItemId())){
				comboItemsMap.put(item.getParentItemId(),new ArrayList<>());
			}
			List<TableOrderItem> tableOrderItems = comboItemsMap.get(item.getParentItemId());
			TableOrderItem tableOrderItem = convert(item,orderItemStatusMap.get(item.getOrderItemId()));
			if (tableOrderItemComposition != null) {
				tableOrderItem.setOrderItemComposition(tableOrderItemComposition);
			}
			tableOrderItems.add(tableOrderItem);
			comboItemsMap.put(item.getParentItemId(),tableOrderItems);
		}
		if(removeAfterInsertion && orderItemStatusMap.containsKey(item.getOrderItemId())){
			orderItemStatusMap.remove(item.getOrderItemId());
		}

	}

	public TableOrder convert(TableOrderMappingDetail map,Map<Integer,OrderItemStatus> orderItemStatusMap) {
		TableOrder order = new TableOrder();
		OrderDetail od = map.getOrder();
		order.setBillingServerTime(od.getBillingServerTime());
		order.setCustomerName(od.getCustomerName());
		order.setGenerateOrderId(od.getGeneratedOrderId());
		order.setOfferCode(od.getOfferCode());
		order.setOrderId(od.getOrderId());
		order.setSource(od.getOrderSource());
		order.setStatus(OrderStatus.valueOf(od.getOrderStatus()));
		order.setTransactionDetail(toTransactionDetail(od, false));

		Map <Integer,List<TableOrderItem>> comboItemsMap = new HashMap<>();
		Map <String, BigDecimal> paidAddOnsMap = new HashMap<>();
		Map <Integer, Boolean> orderIdsBoolMap = new HashMap<>();

		for (com.stpl.tech.kettle.data.kettle.OrderItem item : od.getOrderItems()) {
			orderIdsBoolMap.put(item.getOrderItemId(), Boolean.TRUE);
			constructPaidAddOnsMap(paidAddOnsMap, item);
		}

		// Adding paid add-ons to the parent item
		for (com.stpl.tech.kettle.data.kettle.OrderItem item : od.getOrderItems()) {
			List <TableOrderItem> paidAddOnsList = new ArrayList<>();
			if (paidAddOnsMap.containsKey(item.getProductName()) && paidAddOnsMap.get(item.getProductName()).compareTo(
					BigDecimal.valueOf(item.getQuantity())) >= 0) {
						paidAddOnsMap.put(item.getProductName(), AppUtils.subtract(paidAddOnsMap.get(item.getProductName()),
						BigDecimal.valueOf(item.getQuantity())));
				continue;
			}
			for (com.stpl.tech.kettle.data.kettle.OrderItemAddon addon : item.getOrderItemAddons()) {
				if (addon.getProductId() == -1 && addon.getSource().equals(PRODUCT_SOURCE_SYSTEM_OPTION)) {
					// Looping over items again (using var 'repeatItem') to check which item is paid add-on
					for(com.stpl.tech.kettle.data.kettle.OrderItem repeatItem : od.getOrderItems()) {
						if (addon.getName().equalsIgnoreCase(repeatItem.getProductName())) {
							TableOrderItem toi = convert(repeatItem, addon);
							paidAddOnsList.add(toi);
							break;
						}
					}
				}
			}
		    TableOrderItemComposition tableOrderItemComposition =  TableOrderItemComposition.builder().paidAddOns(paidAddOnsList).build();
			addTableOrderItems(item,orderItemStatusMap,comboItemsMap,order,true, tableOrderItemComposition);
		}

		for (Integer orderItemId : orderItemStatusMap.keySet()) {
			if (!orderIdsBoolMap.containsKey(orderItemId) && orderItemStatusMap.get(orderItemId).getOrderId().equals(od.getOrderId())) {
				constructPaidAddOnsMap(paidAddOnsMap, orderItemStatusMap.get(orderItemId).getOrderItemId());
			}
		}

		for(Integer orderItemId : orderItemStatusMap.keySet()){
			com.stpl.tech.kettle.data.kettle.OrderItem orderItem = orderItemStatusMap.get(orderItemId).getOrderItemId();
			log.info("quantity : {}  : {}",orderItem.getQuantity() , paidAddOnsMap.get(orderItem.getProductName()) );
			if (orderIdsBoolMap.containsKey(orderItemId) ||( paidAddOnsMap.containsKey(orderItem.getProductName()) &&
					paidAddOnsMap.get(orderItem.getProductName()).compareTo(BigDecimal.valueOf(orderItem.getQuantity())) >= 0
			&& orderItemStatusMap.get(orderItemId).getOrderId().equals(od.getOrderId()))) {
				paidAddOnsMap.put(orderItem.getProductName(), AppUtils.subtract(paidAddOnsMap.get(orderItem.getProductName()),
                        BigDecimal.valueOf(orderItem.getQuantity())));
				continue;
			}
			if (orderIdsBoolMap.containsKey(orderItemId)) {
				continue;
			}
			OrderItemStatus orderItemStatus = orderItemStatusMap.get(orderItemId);
			if(od.getOrderId().equals(orderItemStatus.getOrderId())){
				addTableOrderItems(orderItemStatus.getOrderItemId(),orderItemStatusMap,comboItemsMap,order,
						false, null);
			}
		}

		if(!CollectionUtils.isEmpty(comboItemsMap)){
			for(TableOrderItem item : order.getItems()){
				if(comboItemsMap.containsKey(item.getOrderItemId())){
					TableOrderItemComposition tableOrderItemComposition = TableOrderItemComposition.builder()
							.menuProducts(comboItemsMap.get(item.getOrderItemId())).build();

					item.setOrderItemComposition(tableOrderItemComposition);
				}
			}
		}
		return order;
	}

	private void constructPaidAddOnsMap(Map <String, BigDecimal> paidAddOnsMap, com.stpl.tech.kettle.data.kettle.OrderItem item) {
		for (com.stpl.tech.kettle.data.kettle.OrderItemAddon addon : item.getOrderItemAddons()) {
			if (addon.getProductId() == -1 && addon.getSource().equals(PRODUCT_SOURCE_SYSTEM_OPTION)) {
				if (paidAddOnsMap.containsKey(addon.getName())) {
					paidAddOnsMap.put(addon.getName(), paidAddOnsMap.get(addon.getName()).add(addon.getQuantity()));
				} else {
					paidAddOnsMap.put(addon.getName(), addon.getQuantity());
				}
			}
		}
	}

    private static TableOrderItem convert(com.stpl.tech.kettle.data.kettle.OrderItem item) {
        TableOrderItem toi = new TableOrderItem();
		toi.setOrderItemId(item.getOrderItemId());
        toi.setDimension(item.getDimension());
        toi.setItemCode(item.getTaxCode());
        toi.setPrice(item.getPrice());
        toi.setProductId(item.getProductId());
        toi.setProductName(item.getProductName());
        toi.setQuantity(item.getQuantity());
        toi.setTaxAmount(item.getTaxAmount());
        toi.setTotalAmount(item.getTotalAmount());
        return toi;
    }

	private static TableOrderItem convert(com.stpl.tech.kettle.data.kettle.OrderItem item, OrderItemStatus orderItemStatus) {
		TableOrderItem toi = new TableOrderItem();
		toi.setOrderItemId(item.getOrderItemId());
		toi.setDimension(item.getDimension());
		toi.setItemCode(item.getTaxCode());
		toi.setPrice(item.getPrice());
		toi.setProductId(item.getProductId());
		toi.setProductName(item.getProductName());
		toi.setQuantity(item.getQuantity());
		toi.setTaxAmount(item.getTaxAmount());
		toi.setTotalAmount(item.getTotalAmount());
		if(Objects.nonNull(orderItemStatus)){
			toi.setItemStatus(orderItemStatus.getStatus());
			if(OrderStatus.ON_HOLD.value().equalsIgnoreCase(orderItemStatus.getStatus())){
				toi.setIsHoldOn(AppConstants.YES);
			}
		}
		return toi;
	}

	// Use this function for paid add-ons

	private static TableOrderItem convert(com.stpl.tech.kettle.data.kettle.OrderItem item,
										  com.stpl.tech.kettle.data.kettle.OrderItemAddon addon) {
		TableOrderItem toi = new TableOrderItem();
		toi.setOrderItemId(item.getOrderItemId());
		toi.setDimension(item.getDimension());
		toi.setItemCode(item.getTaxCode());
		toi.setPrice(item.getPrice());
		toi.setProductId(item.getProductId());
		toi.setProductName(item.getProductName());

		// quantity retrieved from addon
		toi.setQuantity(addon.getQuantity().intValue());

		// tax amount set to appropriate amount
		toi.setTaxAmount(AppUtils.multiply(AppUtils.divide(item.getTaxAmount(),
				BigDecimal.valueOf(item.getQuantity())),addon.getQuantity()));

		// total amount = amount of each * quantity from addon
		toi.setTotalAmount(item.getPrice().multiply(addon.getQuantity()));

		return toi;
	}

	private void setDispenserShotsInfoToOrderItem(com.stpl.tech.kettle.data.kettle.OrderItem itemData, OrderItem itemDto) {
		if (!CollectionUtils.isEmpty(itemData.getOrderItemDispenserShotsDataSet())) {
			Map<Integer, OrderItemDispenserShotsDataDto> shotsInfoByProduct = itemData.getOrderItemDispenserShotsDataSet().stream().filter(e -> Objects.nonNull(e.getCanisterItemId()) &&
							AppConstants.YES.equalsIgnoreCase(e.getUsedDispenser()))
					.collect(Collectors.toMap(this::getProductIdOfCanister,
							DispenserMapper.INSTANCE::orderItemDispenserShotsDataToOrderItemDispenserShotsDataDto, (e1, e2) -> e1));
			itemDto.setOrderItemDispenserShotsDataDtoMap(shotsInfoByProduct);
		}
		addAllAddonsToDispenserShotsDataDtoMap(itemDto);
	}

	private void addAllAddonsToDispenserShotsDataDtoMap(OrderItem itemDto) {
		if (Objects.nonNull(itemDto.getComposition()) && !CollectionUtils.isEmpty(itemDto.getComposition().getAddons())) {
			itemDto.getComposition().getAddons().forEach(addon -> {
				if (!itemDto.getOrderItemDispenserShotsDataDtoMap().containsKey(addon.getProduct().getProductId())) {
					itemDto.getOrderItemDispenserShotsDataDtoMap().put(addon.getProduct().getProductId(), OrderItemDispenserShotsDataDto.builder()
							.orderItemId(itemDto.getItemId())
							.productId(addon.getProduct().getProductId())
							.productName(addon.getProduct().getName())
							.shortCode(addon.getProduct().getShortCode())
							.build());
				}
			});
		}
	}

	private Integer getProductIdOfCanister(OrderItemDispenserShotsData orderItemDispenserShotsData) {
		return dispenserCanisterCache.getCanisterToProductMap().get(orderItemDispenserShotsData.getCanisterItemId());
	}

	public static OrderDomain convertToKettleWalletOrderNew(Order request){
		OrderDomain orderDomain = new OrderDomain();
		WalletOrder walletOrder = new WalletOrder();
		walletOrder.setRechargeAmount(BigDecimal.ZERO);
		walletOrder.setOfferAmount(BigDecimal.ZERO);
		for(OrderItem wi : request.getOrders()){
			walletOrder.setRechargeAmount(walletOrder.getRechargeAmount().add(wi.getTotalAmount()));
			walletOrder.setOfferAmount(walletOrder.getOfferAmount().add(wi.getOfferAmount()));
		}
		walletOrder.setExtraAmount(BigDecimal.ZERO);
		walletOrder.setSuggestedAmount(BigDecimal.ZERO);
		Order order = request;

		order.setOrders(new ArrayList<>());
		order.setOrderType(AppConstants.ORDER_TYPE_REGULAR);
		order.setStatus(OrderStatus.CREATED);
		orderDomain.setOrder(order);
		orderDomain.setWalletOrder(walletOrder);
		orderDomain.setIncludeReceipts(true);
		return orderDomain;
	}

	public void resolveOrderItemDetails(OrderItem orderItem , Integer unitId){
		if (DesiChaiConsumptionHelper.isPlaceholderProduct(orderItem.getProductId())) {
			Integer pid = getMappedVariant(orderItem);
			orderItem.setProductId(pid == null ? orderItem.getProductId() : pid);
			orderItem.setProductName(productCache.getProductBasicDetail(orderItem.getProductId()).getDetail().getName());
		}
		if (orderItem.getRecipeId() == 0) {
			Integer recipeId = recipeCache.getUnitProductRecipeId(unitId, orderItem.getProductId(), orderItem.getDimension());
			orderItem.setRecipeId(recipeId);
		}
		orderItem.setComboQuantityStrategy(ComboQunantityStrategy.MULTIPLICATION);
	}

	public OrderDomain convertToKettleCompatibleOrder(Order order){
		for(OrderItem oi : order.getOrders()){
			resolveOrderItemDetails(oi,order.getUnitId());
			if(Objects.nonNull(oi.getComposition()) && !CollectionUtils.isEmpty(oi.getComposition().getMenuProducts())){
				List<OrderItem> menuItems = oi.getComposition().getMenuProducts();
				for(OrderItem mi : menuItems){
					resolveOrderItemDetails(mi,order.getUnitId());
				}
			}
			if(Objects.nonNull(oi.getComposition()) && !CollectionUtils.isEmpty(oi.getComposition().getVariants())){
				List<IngredientVariantDetail> variantDetails = oi.getComposition().getVariants();
				for(IngredientVariantDetail variantDetail : variantDetails){
					variantDetail.setId(variantDetail.getProductId());
				}
			}
			if(Objects.isNull(oi.getComplimentaryDetail())){
				oi.setComplimentaryDetail(new ComplimentaryDetail());
			}
		}
		if(isWalletOrder(order)){
			return convertToKettleWalletOrderNew(order);
		}
		OrderDomain orderDomain = new OrderDomain();
		orderDomain.setOrder(order);
		return orderDomain;
	}

	public static boolean isWalletOrder(Order order){
		if(Objects.nonNull(order) && order.isGiftCardOrder()){
			return  true;
		}
		return false;
	}

	public Integer getMappedVariant(com.stpl.tech.kettle.domain.model.OrderItem item) {
		int identifierProduct = DesiChaiConsumptionHelper.placeholderIdentifier(item.getProductId());
		List<IngredientVariantDetail> variants = new ArrayList<>();
		Integer productId = null;
		if (item.getComposition() != null && item.getComposition().getVariants() != null) {
			for (IngredientVariantDetail v : item.getComposition().getVariants()) {

				if (identifierProduct == v.getProductId()) {
					productId = DesiChaiConsumptionHelper.getActualProduct(item.getProductId(), v.getAlias());
				} else {
					variants.add(v);
				}
			}
			item.getComposition().setVariants(variants);
		}
		return productId;
	}

}
