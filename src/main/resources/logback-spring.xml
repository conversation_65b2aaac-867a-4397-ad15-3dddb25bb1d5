<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <include resource="org/springframework/boot/logging/logback/defaults.xml" />
    <springProperty scope="context" name="log.location" source="log.location"/>
    <springProperty scope="context" name="spring.application.name" source="spring.application.name"/>

    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%clr(%d{yy-MM-dd E HH:mm:ss.SSS}){magenta} %clr(%-5p) %clr(${PID}) %clr(---) %clr([%8.15t]) %clr(%-40.40logger{0}) %clr(==>) %white(%msg) %n</pattern>
        </encoder>
    </appender>

    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <Append>true</Append>
        <File>${log.location}/${spring.application.name}.log</File>
        <encoder>
            <pattern>%clr(%d{yy-MM-dd E HH:mm:ss.SSS}){magenta} %clr(%-5p) %clr(${PID}) %clr(---) %clr([%8.15t]) %clr(%-40.40logger{0}) %clr(==>) %white(%msg) %n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.location}/archive/${spring.application.name}/${spring.application.name}-%d{yyyy-MM-dd}.log</fileNamePattern>
        </rollingPolicy>

    </appender>
    <springProfile name="native">
        <root level="INFO">
            <appender-ref ref="CONSOLE" />
            <appender-ref ref="FILE" />
        </root>
    </springProfile>
    <springProfile name="local">
        <root level="INFO">
            <appender-ref ref="CONSOLE" />
            <appender-ref ref="FILE" />
        </root>
    </springProfile>
    <springProfile name="dev">
        <root level="INFO">
            <appender-ref ref="CONSOLE" />
            <appender-ref ref="FILE" />
        </root>
    </springProfile>
    <springProfile name="stage">
        <root level="INFO">
             <appender-ref ref="CONSOLE" />
            <appender-ref ref="FILE" />
        </root>
    </springProfile>
    <springProfile name="uat | prod | sprod">
        <root level="INFO">
            <appender-ref ref="FILE" />
        </root>
    </springProfile>
</configuration>
